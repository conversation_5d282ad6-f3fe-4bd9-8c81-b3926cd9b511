% 偶极子绘图程序测试脚本
% 用于验证程序的基本功能和参数设置

clc; clear; close all;

fprintf('=== 偶极子绘图程序测试 ===\n');

%% 测试1: 参数验证功能
fprintf('\n1. 测试参数验证功能...\n');

% 测试有效参数
direction_type = 1;
data_type = 1;
fprintf('✓ 有效参数测试通过: direction_type=%d, data_type=%d\n', direction_type, data_type);

% 测试无效参数（注释掉以避免错误）
% try
%     direction_type = 5;  % 无效值
%     data_type = 1;
%     run('huitu_3D_best.m');
% catch ME
%     fprintf('✓ 参数验证正常工作: %s\n', ME.message);
% end

%% 测试2: 检查数据文件要求
fprintf('\n2. 检查数据文件要求...\n');

% 期望的偶极子数据字段
expected_receiver_fields = {'receiver_data_x', 'receiver_data_y', 'receiver_data_z', 'receiver_data_pressure'};
expected_das_fields = {'das_data_x', 'das_data_y', 'das_data_z', 'enable_das'};
expected_common_fields = {'time_axis', 'dt', 'num_receivers', 'source_x', 'source_y', 'source_z'};

fprintf('期望的检波器数据字段:\n');
for i = 1:length(expected_receiver_fields)
    fprintf('  - %s\n', expected_receiver_fields{i});
end

fprintf('期望的DAS数据字段:\n');
for i = 1:length(expected_das_fields)
    fprintf('  - %s\n', expected_das_fields{i});
end

fprintf('期望的公共字段:\n');
for i = 1:length(expected_common_fields)
    fprintf('  - %s\n', expected_common_fields{i});
end

%% 测试3: 方向和数据类型组合
fprintf('\n3. 测试方向和数据类型组合...\n');

direction_names = {'X方向', 'Y方向', 'Z方向', '压力'};
data_type_names = {'检波器', 'DAS', '分别显示', '对比显示'};

fprintf('可用的组合:\n');
for dir = 1:4
    for dtype = 1:4
        % 压力数据通常没有DAS，跳过DAS相关选项
        if dir == 4 && (dtype == 2 || dtype == 3 || dtype == 4)
            fprintf('  %s + %s: ❌ (压力数据通常没有DAS)\n', direction_names{dir}, data_type_names{dtype});
        else
            fprintf('  %s + %s: ✓\n', direction_names{dir}, data_type_names{dtype});
        end
    end
end

%% 测试4: 文件名生成逻辑
fprintf('\n4. 测试文件名生成逻辑...\n');

test_combinations = [
    1, 1;  % X方向, 检波器
    2, 2;  % Y方向, DAS
    3, 3;  % Z方向, 分别显示
    1, 4;  % X方向, 对比显示
];

fprintf('预期的文件名:\n');
for i = 1:size(test_combinations, 1)
    dir = test_combinations(i, 1);
    dtype = test_combinations(i, 2);
    
    switch dtype
        case 1
            filename = sprintf('receiver_%s_shot1.png', direction_names{dir});
        case 2
            filename = sprintf('das_%s_shot1.png', direction_names{dir});
        case 3
            filename1 = sprintf('receiver_%s_shot1.png', direction_names{dir});
            filename2 = sprintf('das_%s_shot1.png', direction_names{dir});
            filename = sprintf('%s 和 %s', filename1, filename2);
        case 4
            filename = sprintf('comparison_%s_shot1.png', direction_names{dir});
    end
    
    fprintf('  %s + %s: %s\n', direction_names{dir}, data_type_names{dtype}, filename);
end

%% 测试5: 数据兼容性检查函数
fprintf('\n5. 创建数据兼容性检查函数...\n');

function check_data_compatibility(data_file)
    % 检查数据文件是否与偶极子绘图程序兼容
    
    if ~exist(data_file, 'file')
        fprintf('❌ 数据文件不存在: %s\n', data_file);
        return;
    end
    
    try
        data = load(data_file);
        fprintf('✓ 数据文件加载成功: %s\n', data_file);
        
        % 检查必需字段
        required_fields = {'time_axis', 'dt', 'num_receivers'};
        for i = 1:length(required_fields)
            if isfield(data, required_fields{i})
                fprintf('  ✓ %s: 存在\n', required_fields{i});
            else
                fprintf('  ❌ %s: 缺失\n', required_fields{i});
            end
        end
        
        % 检查偶极子数据字段
        dipole_fields = {'receiver_data_x', 'receiver_data_y', 'receiver_data_z', 'receiver_data_pressure'};
        fprintf('  偶极子检波器数据:\n');
        for i = 1:length(dipole_fields)
            if isfield(data, dipole_fields{i})
                fprintf('    ✓ %s: 存在 (尺寸: %s)\n', dipole_fields{i}, mat2str(size(data.(dipole_fields{i}))));
            else
                fprintf('    ❌ %s: 缺失\n', dipole_fields{i});
            end
        end
        
        % 检查DAS数据字段
        das_fields = {'das_data_x', 'das_data_y', 'das_data_z', 'enable_das'};
        fprintf('  DAS数据:\n');
        for i = 1:length(das_fields)
            if isfield(data, das_fields{i})
                if strcmp(das_fields{i}, 'enable_das')
                    fprintf('    ✓ %s: %s\n', das_fields{i}, data.(das_fields{i}) ? '启用' : '禁用');
                else
                    fprintf('    ✓ %s: 存在 (尺寸: %s)\n', das_fields{i}, mat2str(size(data.(das_fields{i}))));
                end
            else
                fprintf('    ❌ %s: 缺失\n', das_fields{i});
            end
        end
        
    catch ME
        fprintf('❌ 数据文件加载失败: %s\n', ME.message);
    end
end

% 测试数据兼容性检查
test_file = 'oujizi_yingdiceng_13receiver_results.mat';
check_data_compatibility(test_file);

fprintf('\n=== 测试完成 ===\n');
fprintf('如果所有测试通过，可以运行 huitu_3D_best.m 进行绘图\n');

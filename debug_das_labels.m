% 调试DAS深度标签计算
clc; clear;

% 加载数据
load('acoustic_logging_3D_13receiver_with_DAS_results.mat');

fprintf('=== DAS数据调试 ===\n');
fprintf('震源位置: %.2f米\n', source_z);
fprintf('DAS启用状态: %d\n', enable_das);

% 检查gauge_centers
if exist('gauge_centers', 'var')
    fprintf('\n=== gauge_centers数据 ===\n');
    for i = 1:length(gauge_centers)
        fprintf('gauge_centers(%d) = %.2f米\n', i, gauge_centers(i));
    end
else
    fprintf('没有gauge_centers变量！\n');
end

% 模拟绘图程序的DAS深度标签计算
data_type = 'das';
config.receiver_start = 1;
config.num_receivers_to_show = 13;

trace_start = config.receiver_start;
trace_end = min(trace_start + config.num_receivers_to_show - 1, num_das_points);
actual_traces = trace_end - trace_start + 1;

fprintf('\n=== DAS绘图参数 ===\n');
fprintf('data_type = %s\n', data_type);
fprintf('trace_start = %d\n', trace_start);
fprintf('trace_end = %d\n', trace_end);
fprintf('actual_traces = %d\n', actual_traces);
fprintf('num_das_points = %d\n', num_das_points);

fprintf('\n=== DAS深度标签计算 ===\n');
depth_labels = cell(1, actual_traces);
for i = 1:actual_traces
    actual_trace_num = trace_start + i - 1;
    
    fprintf('显示道%d -> actual_trace_num = %d\n', i, actual_trace_num);
    
    % 根据数据类型获取深度信息（复制绘图程序的逻辑）
    if strcmp(data_type, 'das') && exist('gauge_centers', 'var')
        absolute_depth = gauge_centers(actual_trace_num);
        fprintf('  使用gauge_centers: %.2f米\n', absolute_depth);
    elseif exist('receiver_z', 'var')
        absolute_depth = receiver_z(actual_trace_num);
        fprintf('  使用receiver_z: %.2f米\n', absolute_depth);
    else
        % 如果没有具体深度信息，使用估算值
        absolute_depth = source_z + first_receiver_offset + ...
                       (actual_trace_num-1) * receiver_spacing;
        fprintf('  使用估算值: %.2f米\n', absolute_depth);
    end
    
    depth_labels{i} = sprintf('%.2f', absolute_depth);
    fprintf('  最终标签: %s\n', depth_labels{i});
end

fprintf('\n=== 最终深度标签数组 ===\n');
for i = 1:length(depth_labels)
    fprintf('Y轴标签%d: %s米\n', i, depth_labels{i});
end

% 检查是否有变量名冲突
fprintf('\n=== 变量检查 ===\n');
loaded_data = load('acoustic_logging_3D_13receiver_with_DAS_results.mat');
fprintf('loaded_data中是否有gauge_centers: %d\n', isfield(loaded_data, 'gauge_centers'));
if isfield(loaded_data, 'gauge_centers')
    fprintf('loaded_data.gauge_centers(1) = %.2f\n', loaded_data.gauge_centers(1));
end

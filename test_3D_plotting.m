% 三维声波测井绘图程序测试脚本
% 测试所有显示模式和功能

clc; clear; close all;

fprintf('=== 三维声波测井绘图程序测试 ===\n');

% 数据文件
data_filename = 'acoustic_logging_3D_13receiver_with_DAS_results.mat';

% 检查数据文件是否存在
if ~exist(data_filename, 'file')
    error('数据文件不存在: %s', data_filename);
end

fprintf('数据文件: %s\n', data_filename);

%% 测试1: 检波器数据显示
fprintf('\n--- 测试1: 检波器数据显示 ---\n');
test_config = struct();
test_config.data_type = 'receiver';
test_config.amplitude_mode = 6;  % 第一道显示原波形
test_config.num_receivers_to_show = 13;
test_config.receiver_start = 1;
run_test_case(test_config, '检波器数据_模式6');

%% 测试2: DAS数据显示
fprintf('\n--- 测试2: DAS数据显示 ---\n');
test_config.data_type = 'das';
test_config.amplitude_mode = 1;  % 传统归一化
run_test_case(test_config, 'DAS数据_模式1');

%% 测试3: 对比显示
fprintf('\n--- 测试3: 检波器与DAS对比显示 ---\n');
test_config.data_type = 'compare';
test_config.amplitude_mode = 1;  % 传统归一化
run_test_case(test_config, '对比显示');

%% 测试4: 分别显示
fprintf('\n--- 测试4: 分别显示检波器和DAS ---\n');
test_config.data_type = 'both';
test_config.amplitude_mode = 3;  % 中等保留振幅差异
run_test_case(test_config, '分别显示');

%% 测试5: 不同振幅模式
fprintf('\n--- 测试5: 不同振幅显示模式 ---\n');
for mode = 1:5
    fprintf('测试振幅模式 %d\n', mode);
    test_config.data_type = 'receiver';
    test_config.amplitude_mode = mode;
    test_config.num_receivers_to_show = 10;  % 显示前10个检波器
    run_test_case(test_config, sprintf('振幅模式%d', mode));
end

fprintf('\n=== 所有测试完成 ===\n');
fprintf('请检查 Picture_3D_AcousticLogging 文件夹中的图片\n');

%% 辅助函数
function run_test_case(config, test_name)
    try
        % 修改huitu_3D_best.m中的配置并运行
        modify_and_run_3d_plot(config);
        fprintf('✓ %s - 成功\n', test_name);
    catch ME
        fprintf('✗ %s - 失败: %s\n', test_name, ME.message);
    end
end

function modify_and_run_3d_plot(config)
    % 读取原始文件
    original_file = 'huitu_3D_best.m';
    temp_file = 'temp_huitu_3D.m';
    
    % 读取文件内容
    fid = fopen(original_file, 'r');
    if fid == -1
        error('无法打开文件: %s', original_file);
    end
    content = fread(fid, '*char')';
    fclose(fid);
    
    % 修改配置参数
    content = regexprep(content, "data_type = '[^']*';", sprintf("data_type = '%s';", config.data_type));
    content = regexprep(content, 'amplitude_mode = \d+;', sprintf('amplitude_mode = %d;', config.amplitude_mode));
    
    if isfield(config, 'num_receivers_to_show')
        content = regexprep(content, 'config\.num_receivers_to_show = \d+;', ...
                           sprintf('config.num_receivers_to_show = %d;', config.num_receivers_to_show));
    end
    
    if isfield(config, 'receiver_start')
        content = regexprep(content, 'config\.receiver_start = \d+;', ...
                           sprintf('config.receiver_start = %d;', config.receiver_start));
    end
    
    % 写入临时文件
    fid = fopen(temp_file, 'w');
    if fid == -1
        error('无法创建临时文件: %s', temp_file);
    end
    fwrite(fid, content, 'char');
    fclose(fid);
    
    % 运行临时文件
    run(temp_file);
    
    % 删除临时文件
    delete(temp_file);
end

# 三维声波测井绘图程序使用说明

## 程序概述

`huitu_3D_best.m` 是基于原有二维绘图程序 `huitu_best.m` 开发的三维声波测井数据绘图程序，保留了原有的所有功能，并扩展支持三维检波器和DAS数据的显示。

## 主要功能

### 1. 数据类型支持
- **检波器数据**: 传统的压力检波器数据
- **DAS数据**: 分布式声学传感（Distributed Acoustic Sensing）应变率数据
- **对比显示**: 检波器和DAS数据叠加在同一图中显示
- **分别显示**: 检波器和DAS数据分别生成图片

### 2. 显示模式
程序支持4种主要显示模式：
- `'receiver'` - 只显示检波器数据
- `'das'` - 只显示DAS数据
- `'both'` - 分别显示检波器和DAS数据（生成两张图片）
- `'compare'` - 叠加对比显示（检波器为黑色，DAS为红色）

### 3. 振幅处理模式
保留了原程序的6种振幅处理模式：
1. **传统归一化** - 所有道振幅相同，清晰显示
2. **轻微保留振幅差异** - 30%保留原始振幅差异
3. **中等保留振幅差异** - 50%保留原始振幅差异
4. **强烈保留振幅差异** - 80%保留原始振幅差异
5. **不进行归一化** - 显示原始波形
6. **第一道显示原波形** - 第一道显示原始振幅，其余道归一化

## 使用方法

### 基本配置

在程序开头的用户配置区域修改以下参数：

```matlab
% 1. 数据文件选择
data_filename = 'acoustic_logging_3D_13receiver_with_DAS_results.mat';

% 2. 显示类型选择
data_type = 'receiver';  % 'receiver', 'das', 'both', 'compare'

% 3. 检波器/DAS显示配置
config.num_receivers_to_show = 13;  % 显示的检波器/DAS数量
config.receiver_start = 1;          % 起始检波器/DAS编号

% 4. 振幅显示模式
amplitude_mode = 6;  % 1-6选择不同的振幅处理模式
```

### 高级配置

```matlab
% 声波测井显示参数
config.trace_spacing = 1.5;        % 道间距（垂直偏移）
config.amplitude_scale = 0.8;      % 检波器振幅缩放因子
config.das_amplitude_scale = 0.8;  % DAS振幅缩放因子
config.time_range = [0, 0.004];    % 显示时间范围[开始, 结束]秒
config.fill_positive = true;       % 是否填充正振幅
config.fill_color = [0.8, 0.2, 0.2];     % 检波器填充颜色
config.das_fill_color = [0.2, 0.2, 0.8]; % DAS填充颜色

% 图片质量配置
config.dpi = 400;                  % 图片分辨率
config.figure_size = [1200, 900];  % 图形窗口大小
```

## 输出结果

### 文件保存
- 图片保存在 `Picture_3D_AcousticLogging` 文件夹中
- 文件命名格式：`三维声波测井_[数据类型]_炮点_[炮点号].png`
- 支持高分辨率输出（默认400 DPI）

### 图片类型
1. **检波器数据图**: `三维声波测井_receiver_炮点_1.png`
2. **DAS数据图**: `三维声波测井_das_炮点_1.png`
3. **对比图**: `三维声波测井_对比_炮点_1.png`

## 数据要求

程序需要包含以下变量的MAT文件：

### 必需变量
- `receiver_data`: 检波器数据矩阵 (时间 × 检波器数)
- `time_axis`: 时间轴向量
- `num_receivers`: 检波器数量
- `dt`: 时间采样间隔
- `source_x`, `source_y`, `source_z`: 震源位置
- `receiver_x`, `receiver_y`, `receiver_z`: 检波器位置

### DAS相关变量（可选）
- `das_data`: DAS数据矩阵 (时间 × DAS点数)
- `enable_das`: DAS系统启用标志
- `num_das_points`: DAS标距点数量
- `gauge_centers`: DAS标距中心位置

## 使用示例

### 示例1：显示检波器数据
```matlab
% 修改配置
data_type = 'receiver';
amplitude_mode = 6;  % 第一道显示原波形
config.num_receivers_to_show = 13;

% 运行程序
run('huitu_3D_best.m');
```

### 示例2：对比显示检波器和DAS
```matlab
% 修改配置
data_type = 'compare';
amplitude_mode = 1;  % 传统归一化
config.fill_positive = true;

% 运行程序
run('huitu_3D_best.m');
```

### 示例3：分别显示所有数据
```matlab
% 修改配置
data_type = 'both';
amplitude_mode = 3;  % 中等保留振幅差异

% 运行程序
run('huitu_3D_best.m');
```

## 程序特点

### 保留原有功能
- 完全保留了 `huitu_best.m` 的所有绘图功能
- 支持所有原有的振幅处理模式
- 保持相同的图片质量和样式

### 三维数据适配
- 自动适配三维数据结构
- 支持检波器和DAS数据的独立处理
- 智能的数据类型检测和错误处理

### 增强功能
- 新增DAS数据显示支持
- 新增检波器/DAS对比显示模式
- 改进的深度标签显示
- 更灵活的配置选项

## 注意事项

1. **数据文件**: 确保数据文件存在且包含必需的变量
2. **显示范围**: 检波器显示数量不能超过实际数据中的检波器数量
3. **时间范围**: 时间显示范围应在数据的有效时间范围内
4. **内存使用**: 高分辨率图片可能需要较多内存
5. **DAS数据**: 如果选择DAS相关模式但数据中没有DAS数据，程序会自动切换到检波器模式

## 故障排除

### 常见问题
1. **数据加载失败**: 检查数据文件路径和文件名
2. **显示范围错误**: 调整 `num_receivers_to_show` 和 `receiver_start` 参数
3. **图片质量问题**: 调整 `dpi` 和 `figure_size` 参数
4. **振幅显示异常**: 尝试不同的 `amplitude_mode` 设置

### 调试建议
- 运行前检查MATLAB命令窗口的输出信息
- 查看生成的图片文件确认效果
- 根据需要调整配置参数重新运行

% 调试深度标签计算
clc; clear;

% 加载数据
load('acoustic_logging_3D_13receiver_with_DAS_results.mat');

fprintf('=== 检波器位置调试信息 ===\n');
fprintf('震源位置: %.2f米\n', source_z);
fprintf('第一个检波器源距设置: %.2f米\n', first_receiver_offset);
fprintf('检波器间距: %.2f米\n', receiver_spacing);

fprintf('\n=== 所有检波器位置 ===\n');
for i = 1:num_receivers
    fprintf('检波器%2d: 深度%.2f米, 源距%.2f米\n', i, receiver_z(i), receiver_z(i)-source_z);
end

% 模拟绘图程序的深度标签计算
config.receiver_start = 1;
config.num_receivers_to_show = 13;

trace_start = config.receiver_start;
trace_end = min(trace_start + config.num_receivers_to_show - 1, num_receivers);
actual_traces = trace_end - trace_start + 1;

fprintf('\n=== 绘图程序深度标签计算 ===\n');
fprintf('trace_start = %d\n', trace_start);
fprintf('trace_end = %d\n', trace_end);
fprintf('actual_traces = %d\n', actual_traces);

depth_labels = cell(1, actual_traces);
for i = 1:actual_traces
    actual_trace_num = trace_start + i - 1;
    depth = receiver_z(actual_trace_num);
    depth_labels{i} = sprintf('%.2f', depth);
    
    fprintf('显示道%d -> 检波器%d -> 深度%.2f米\n', i, actual_trace_num, depth);
end

fprintf('\n=== 深度标签数组 ===\n');
for i = 1:length(depth_labels)
    fprintf('标签%d: %s\n', i, depth_labels{i});
end

% 检查是否有任何偏移问题
fprintf('\n=== 验证第一道应该显示的深度 ===\n');
expected_first_depth = source_z + first_receiver_offset;
actual_first_depth = receiver_z(1);
fprintf('期望第一道深度: %.2f米\n', expected_first_depth);
fprintf('实际第一道深度: %.2f米\n', actual_first_depth);
fprintf('是否匹配: %s\n', iif(abs(expected_first_depth - actual_first_depth) < 0.001, '是', '否'));

function result = iif(condition, true_value, false_value)
    if condition
        result = true_value;
    else
        result = false_value;
    end
end

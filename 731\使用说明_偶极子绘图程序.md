# 三维偶极子声波测井绘图程序使用说明

## 程序概述

`huitu_3D_best.m` 已经修改为支持偶极子数据的多方向显示程序。该程序可以显示X、Y、Z方向的检波器数据和DAS数据。

## 主要功能

### 1. 方向选择功能
程序现在支持选择不同的数据方向：
- **X方向** (direction_type = 1): 偶极子主要响应方向
- **Y方向** (direction_type = 2): 横向响应分析
- **Z方向** (direction_type = 3): 纵向响应分析  
- **压力** (direction_type = 4): 压力数据对比分析

### 2. 数据类型选择
在选定方向的基础上，可以选择显示类型：
- **检波器数据** (data_type = 1): 只显示检波器数据
- **DAS数据** (data_type = 2): 只显示DAS数据
- **分别显示** (data_type = 3): 检波器和DAS数据分别绘图
- **对比显示** (data_type = 4): 检波器和DAS数据叠加对比

## 使用方法

### 基本配置

在程序开头修改以下参数：

```matlab
% 1. 数据文件选择
data_filename = 'oujizi_yingdiceng_13receiver_results.mat';

% 2. 方向选择（偶极子数据特有）
direction_type = 1;  % 1=X方向, 2=Y方向, 3=Z方向, 4=压力

% 3. 数据类型选择
data_type = 1;  % 1=检波器, 2=DAS, 3=分别显示, 4=对比显示
```

### 使用示例

#### 示例1：显示X方向检波器数据
```matlab
direction_type = 1;  % X方向
data_type = 1;       % 检波器数据
run('huitu_3D_best.m');
```

#### 示例2：对比显示Y方向的检波器和DAS数据
```matlab
direction_type = 2;  % Y方向
data_type = 4;       % 对比显示
run('huitu_3D_best.m');
```

#### 示例3：分别显示Z方向的检波器和DAS数据
```matlab
direction_type = 3;  % Z方向
data_type = 3;       % 分别显示
run('huitu_3D_best.m');
```

#### 示例4：显示压力数据
```matlab
direction_type = 4;  % 压力
data_type = 1;       % 检波器数据（压力通常没有DAS数据）
run('huitu_3D_best.m');
```

## 输出文件

程序会根据选择的方向和数据类型自动生成带标识的文件名：

- `receiver_X方向_shot1.png` - X方向检波器数据
- `das_Y方向_shot1.png` - Y方向DAS数据
- `comparison_Z方向_shot1.png` - Z方向对比显示
- `receiver_压力_shot1.png` - 压力检波器数据

## 数据要求

程序期望的偶极子数据文件应包含以下字段：

### 检波器数据
- `receiver_data_x` - X方向速度数据
- `receiver_data_y` - Y方向速度数据
- `receiver_data_z` - Z方向速度数据
- `receiver_data_pressure` - 压力数据

### DAS数据（可选）
- `das_data_x` - X方向应变率数据
- `das_data_y` - Y方向应变率数据
- `das_data_z` - Z方向应变率数据
- `enable_das` - DAS系统启用标志

### 其他必需字段
- `time_axis` - 时间轴
- `dt` - 时间步长
- `num_receivers` - 检波器数量
- `source_x`, `source_y`, `source_z` - 震源位置
- `first_receiver_offset` - 第一个检波器偏移

## 注意事项

1. **数据兼容性**: 程序会自动检查选定方向的数据是否可用
2. **自动降级**: 如果DAS数据不可用，程序会自动切换到检波器数据显示
3. **错误处理**: 程序包含参数验证，确保输入值在有效范围内
4. **文件命名**: 输出文件名会自动包含方向信息，避免覆盖

## 故障排除

### 常见错误
1. **"direction_type必须在1-4之间"**: 检查方向选择参数
2. **"data_type必须在1-4之间"**: 检查数据类型选择参数
3. **"选定方向没有可用的数据"**: 检查数据文件是否包含所需的方向数据

### 数据检查
程序运行时会显示详细的数据可用性信息：
```
==================== 偶极子数据类型检查 ====================
检波器数据:
  X方向: 可用
  Y方向: 可用
  Z方向: 可用
  压力: 可用
DAS数据:
  X方向: 可用
  Y方向: 可用
  Z方向: 可用
```

根据这些信息可以判断哪些方向和数据类型可用。

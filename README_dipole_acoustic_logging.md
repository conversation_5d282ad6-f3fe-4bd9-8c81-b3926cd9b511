# 单向偶极子声波测井FDTD正演模拟程序

## 程序概述

本程序是基于有限差分时域（FDTD）方法的三维单向偶极子声波测井正演模拟程序。程序参照了单极子程序的参数设置，保持了偶极子震源配置，并加入了DAS（分布式声学传感）接收系统，支持X、Y方向数据采集和频散计算。

## 主要特性

### 1. 震源配置
- **单向偶极子震源**：X方向相邻两点反向激发
- **Ricker子波**：可调频率（默认8000Hz）
- **震源位置**：井筒中心，深度1.0米

### 2. 接收系统
- **多检波器阵列**：沿井筒轴向布置，间距0.15米
- **多方向数据采集**：
  - X方向速度（偶极子主要响应）
  - Y方向速度（横向响应分析）
  - 压力数据（对比分析）
- **DAS系统**：
  - 与检波器位置对应
  - 标距长度0.4米
  - 应变率测量

### 3. 物理模型
- **井筒内流体**：vp=1500m/s, vs=0m/s, ρ=1000kg/m³
- **地层岩石**：vp=4000m/s, vs=2300m/s, ρ=2500kg/m³
- **井筒半径**：0.1米
- **计算域**：1.0×1.0×6.0米

### 4. 数值参数
- **网格设计**：每波长25个网格点，控制频散
- **时间步长**：CFL条件自动计算
- **边界条件**：PML吸收边界
- **频散控制**：二阶FDTD，频散误差估计

## 文件说明

### 主程序文件
- `unidirectional_dipole_acoustic_logging.m` - 主要的FDTD正演模拟程序
- `plot_dipole_results.m` - 结果可视化脚本

### 参考文件
- `oujizi_kuaisdiceng copy.m` - 原始偶极子程序
- `danjizi_yingdiceng.m` - 参考的单极子程序

## 使用方法

### 1. 运行正演模拟

```matlab
% 运行主程序
run('unidirectional_dipole_acoustic_logging.m')
```

### 2. 快速参数调整

在程序开头的"快速设置区域"可以调整主要参数：

```matlab
% 时间步数设置
CUSTOM_TIME_STEPS = 20000;  % 增加此值可获得更长的记录时间

% 计算精度设置
CFL_NUMBER = 0.3;          % 0.2(高精度), 0.3(标准), 0.4(快速)

% 震源频率设置
SOURCE_FREQUENCY = 8000;   % 主频率(Hz)

% 进度显示设置
PROGRESS_INTERVAL = 200;   % 进度显示间隔
```

### 3. 结果可视化

```matlab
% 运行画图脚本
run('plot_dipole_results.m')
```

### 4. 数据调用示例

```matlab
% 加载结果数据
load('unidirectional_dipole_acoustic_logging_*receiver_results.mat');

% 显示检波器X方向数据
figure; 
imagesc(time_axis*1e6, 1:num_receivers, receiver_data_x');
xlabel('时间 (μs)'); 
ylabel('检波器道数'); 
title('检波器X方向速度数据');

% 显示DAS数据（如果启用）
if enable_das
    figure; 
    imagesc(time_axis*1e6, 1:num_das_points, das_data');
    xlabel('时间 (μs)'); 
    ylabel('DAS标距点'); 
    title('DAS应变率数据');
end
```

## 输出数据

程序会保存包含以下变量的.mat文件：

### 检波器数据
- `receiver_data_x` - X方向速度数据
- `receiver_data_y` - Y方向速度数据  
- `receiver_data_pressure` - 压力数据

### DAS数据（如果启用）
- `das_data` - DAS应变率数据
- `gauge_centers` - 标距中心位置

### 几何参数
- `receiver_x`, `receiver_y`, `receiver_z` - 检波器位置
- `source_x`, `source_y`, `source_z` - 震源位置
- `source_distances` - 源距

### 物理参数
- `vp_fluid`, `vs_fluid`, `rho_fluid` - 流体参数
- `vp_rock`, `vs_rock`, `rho_rock` - 岩石参数
- `f0` - 震源频率

### 数值参数
- `dt`, `dx`, `dy`, `dz` - 时空间距
- `lambda_min` - 最小波长
- `dispersion_error` - 频散误差估计

## 计算建议

### 时间步数选择
- **看直达波**：10000-15000步
- **看反射波**：15000-25000步  
- **看多次反射**：25000-40000步

### 计算精度
- **快速测试**：CFL_NUMBER = 0.4
- **标准计算**：CFL_NUMBER = 0.3
- **高精度**：CFL_NUMBER = 0.2

### 内存需求
- 程序会自动估计内存需求
- 建议使用8GB以上内存的计算机
- 大规模计算建议使用16GB以上内存

## 频散控制

程序包含完整的频散分析和控制：

1. **网格设计**：每波长25个网格点
2. **时间步长**：CFL条件+频率条件双重约束
3. **频散估计**：二阶FDTD频散误差公式
4. **质量评估**：自动检查和建议

## 注意事项

1. **DAS系统默认启用**，如需禁用请设置 `enable_das = false`
2. **实时显示已取消**以提高计算效率
3. **边界条件**使用PML吸收边界，减少反射干扰
4. **震源配置**为X方向偶极子，适合分析横波响应
5. **数据格式**兼容MATLAB，便于后续处理和分析

## 技术支持

如有问题请检查：
1. MATLAB版本兼容性
2. 内存是否充足
3. 参数设置是否合理
4. 频散控制是否有效

程序运行完成后会自动生成详细的参数报告和数据调用示例。

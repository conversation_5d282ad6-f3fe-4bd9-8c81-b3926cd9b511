% 简单的语法测试
clc; clear; close all;

% 测试基本变量
CUSTOM_TIME_STEPS = 20000;
CFL_NUMBER = 0.3;
SOURCE_FREQUENCY = 8000;
PROGRESS_INTERVAL = 200;

fprintf('=== 快速设置信息 ===\n');
fprintf('自定义时间步数: %d\n', CUSTOM_TIME_STEPS);
fprintf('CFL数: %.1f\n', CFL_NUMBER);
fprintf('震源频率: %.0f Hz\n', SOURCE_FREQUENCY);
fprintf('进度显示间隔: 每%d步\n', PROGRESS_INTERVAL);

% 测试DAS相关变量
enable_das = true;
gauge_overlap = 0.5;

if enable_das
    fprintf('DAS系统: 启用\n');
    fprintf('标距重合比例: %.1f%%\n', gauge_overlap*100);
else
    fprintf('DAS系统: 禁用\n');
end

fprintf('测试完成，没有语法错误\n');

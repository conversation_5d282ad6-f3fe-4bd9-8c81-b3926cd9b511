# 三维声波测井绘图程序 - 简化使用说明

## 快速使用

### 1. 数据类型选择（第17行）
```matlab
data_type = 1;  % 选择数据类型：1=检波器, 2=DAS, 3=分别显示, 4=对比显示
```

**选项说明：**
- `1` - 只显示检波器数据
- `2` - 只显示DAS数据  
- `3` - 同时显示检波器和DAS数据（生成两张图片）
- `4` - 对比显示检波器和DAS数据（叠加在同一图中）

### 2. 振幅显示模式（第26行）
```matlab
amplitude_mode = 6;  % 1-6选择不同的振幅处理模式
```

**模式说明：**
1. **传统归一化** - 所有道振幅相同，清晰显示
2. **轻微保留振幅差异** - 30%保留原始振幅差异
3. **中等保留振幅差异** - 50%保留原始振幅差异
4. **强烈保留振幅差异** - 80%保留原始振幅差异
5. **不进行归一化** - 显示原始波形
6. **第一道显示原波形** - 第一道显示原始振幅，其余道归一化

### 3. 显示配置（第29-30行）
```matlab
config.num_receivers_to_show = 13;  % 显示的检波器/DAS数量
config.receiver_start = 1;          % 起始检波器/DAS编号
```

## 常用设置组合

### 检波器数据显示
```matlab
data_type = 1;           % 检波器数据
amplitude_mode = 6;      % 第一道原波形
```

### DAS数据显示
```matlab
data_type = 2;           % DAS数据
amplitude_mode = 1;      % 传统归一化
```

### 对比显示
```matlab
data_type = 4;           % 对比显示
amplitude_mode = 1;      % 传统归一化
```

### 分别显示所有数据
```matlab
data_type = 3;           % 分别显示
amplitude_mode = 3;      % 中等保留振幅差异
```

## 输出结果

程序会在 `Picture_3D_AcousticLogging` 文件夹中生成高清图片：

- **检波器图片**: `三维声波测井_receiver_炮点_1.png`
- **DAS图片**: `三维声波测井_das_炮点_1.png`
- **对比图片**: `三维声波测井_对比_炮点_1.png`

## 运行方法

1. 修改第17行的 `data_type` 数值（1-4）
2. 可选：修改第26行的 `amplitude_mode` 数值（1-6）
3. 运行程序：`run('huitu_3D_best.m')`

## 注意事项

- 确保数据文件 `acoustic_logging_3D_13receiver_with_DAS_results.mat` 存在
- 图片保存为400 DPI高分辨率PNG格式
- 程序会自动创建输出文件夹
- 每次运行会覆盖同名的图片文件

## 故障排除

**问题**: 显示"DAS数据不可用"
**解决**: 程序会自动切换到检波器数据显示

**问题**: 图片质量不满意
**解决**: 调整第26行的 `amplitude_mode` 参数

**问题**: 显示道数不对
**解决**: 检查第29-30行的显示配置参数
